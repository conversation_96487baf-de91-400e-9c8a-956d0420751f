'use client';

import { useState, useEffect } from 'react';
import { MdSave, MdCancel, MdCloudUpload, MdImage, MdInfo } from 'react-icons/md';
// Note: deleteFileByUrl is now handled via API call since it requires server-side access
import DragDropUpload from './DragDropUpload';
import BatchDuplicateHandler from './BatchDuplicateHandler';
import DuplicateConfirmationModal from './DuplicateConfirmationModal';
import FileReplacementModal from './FileReplacementModal';

export default function ThreeSixtyForm({ 
  threeSixty = null, 
  onSave, 
  onCancel, 
  isLoading = false 
}) {
  const [formData, setFormData] = useState({
    name: '',
    url: '',
    originalFileName: '',
    priority: 0,
  });
  const [errors, setErrors] = useState({});
  const [imageFile, setImageFile] = useState(null);
  const [imagePreview, setImagePreview] = useState('');
  const [uploading, setUploading] = useState(false);
  const [dragDropFiles, setDragDropFiles] = useState([]);
  const [uploadMode, setUploadMode] = useState('single'); // 'single' or 'multiple'

  // Duplicate detection states
  const [duplicateCheck, setDuplicateCheck] = useState({
    isChecking: false,
    duplicates: [],
    newFiles: [],
    showHandler: false,
    singleFileMode: false,
    pendingFile: null
  });
  const [showSingleDuplicateModal, setShowSingleDuplicateModal] = useState(false);
  const [showFileReplacementModal, setShowFileReplacementModal] = useState(false);
  const [fileReplacementInfo, setFileReplacementInfo] = useState(null);
  const [uploadResults, setUploadResults] = useState({
    successful: [],
    failed: [],
    skipped: []
  });

  // Initialize form data when threeSixty prop changes
  useEffect(() => {
    if (threeSixty) {
      setFormData({
        name: threeSixty.name || '',
        url: threeSixty.url || '',
        originalFileName: threeSixty.originalFileName || '',
        priority: threeSixty.priority || 0,
      });
      setImagePreview(threeSixty.url || '');
      setUploadMode('single'); // Editing mode uses single upload
    } else {
      setFormData({
        name: '',
        url: '',
        originalFileName: '',
        priority: 0,
      });
      setImagePreview('');
      setUploadMode('multiple'); // New creation defaults to multiple upload
    }
  }, [threeSixty]);

  const handleInputChange = (e) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseInt(value) || 0 : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleImageChange = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      if (files.length === 1) {
        // Single file - existing behavior with auto-name population
        const file = files[0];
        setImageFile(file);

        // Auto-populate name field if it's empty (for new uploads)
        const autoName = file.name.replace(/\.[^/.]+$/, ''); // Remove extension
        setFormData(prev => ({
          ...prev,
          originalFileName: file.name,
          // Only auto-populate name if it's currently empty (new upload)
          name: prev.name.trim() === '' ? autoName : prev.name
        }));

        // Create preview
        const reader = new FileReader();
        reader.onload = (e) => {
          setImagePreview(e.target.result);
        };
        reader.readAsDataURL(file);
      } else {
        // Multiple files - trigger multi-upload
        handleMultipleUpload(files);
      }
    }
  };

  // Handle drag and drop files selection
  const handleDragDropFiles = (files) => {
    setDragDropFiles(files);
    setErrors(prev => ({ ...prev, submit: '' })); // Clear any previous errors
  };

  // Check for duplicate filenames before upload
  const checkForDuplicates = async (files) => {
    setDuplicateCheck(prev => ({ ...prev, isChecking: true }));

    try {
      const filenames = files.map(file => file.name);

      const response = await fetch('/api/360s/check-duplicates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ filenames }),
      });

      const result = await response.json();

      if (result.success) {
        setDuplicateCheck({
          isChecking: false,
          duplicates: result.data.duplicates,
          newFiles: result.data.newFiles,
          showHandler: result.data.duplicates.length > 0
        });

        return result.data;
      } else {
        throw new Error(result.message || 'Failed to check duplicates');
      }
    } catch (error) {
      console.error('Error checking duplicates:', error);
      setErrors({ submit: 'Failed to check for duplicate files. Please try again.' });
      setDuplicateCheck(prev => ({ ...prev, isChecking: false }));
      return null;
    }
  };

  // Enhanced multiple upload with duplicate detection
  const handleMultipleUpload = async (filesToUpload = null) => {
    const files = filesToUpload || dragDropFiles.map(f => f.file);

    if (!files || files.length === 0) {
      setErrors({ submit: 'No files selected for upload' });
      return;
    }

    // First, check for duplicates
    const duplicateData = await checkForDuplicates(files);
    if (!duplicateData) return; // Error occurred during duplicate check

    // If duplicates found, show handler and wait for resolution
    if (duplicateData.duplicates.length > 0) {
      return; // Handler will take over from here
    }

    // No duplicates, proceed with normal upload
    await processFileUploads(files, []);
  };

  // Process file uploads with duplicate resolutions
  const processFileUploads = async (allFiles, duplicateResolutions = []) => {
    setUploading(true);
    const results = {
      successful: [],
      failed: [],
      skipped: []
    };

    try {
      // Create file processing map
      const fileMap = new Map();
      allFiles.forEach(file => {
        const nameWithoutExt = file.name.replace(/\.[^/.]+$/, '');
        fileMap.set(nameWithoutExt, file);
      });

      // Process duplicate resolutions first
      for (const resolution of duplicateResolutions) {
        const file = fileMap.get(resolution.nameWithoutExt);
        if (!file) continue;

        if (resolution.action === 'skip') {
          results.skipped.push({
            filename: file.name,
            reason: 'User chose to skip duplicate'
          });
          continue;
        }

        if (resolution.action === 'replace') {
          try {
            // Update status for drag drop files
            if (dragDropFiles.length > 0) {
              setDragDropFiles(prev => prev.map(f =>
                f.file === file ? { ...f, status: 'uploading' } : f
              ));
            }

            // Upload replacement file
            const formData = new FormData();
            formData.append('files', file);

            const uploadResponse = await fetch('/api/upload/360s', {
              method: 'POST',
              body: formData,
            });

            if (!uploadResponse.ok) {
              const errorText = await uploadResponse.text();
              console.error('Multiple file upload response error:', {
                status: uploadResponse.status,
                statusText: uploadResponse.statusText,
                body: errorText,
                file: file.name
              });
              throw new Error(`Upload failed: ${uploadResponse.status} ${uploadResponse.statusText}`);
            }

            const uploadResult = await uploadResponse.json();

            if (uploadResult.success && uploadResult.data.length > 0) {
              // Update existing record instead of creating new one
              const updateData = {
                url: uploadResult.data[0].url,
                originalFileName: file.name,
              };

              const updateResponse = await fetch(`/api/360s/${resolution.existingData._id}`, {
                method: 'PATCH',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify(updateData),
              });

              if (!updateResponse.ok) {
                const errorText = await updateResponse.text();
                console.error('Multiple file update response error:', {
                  status: updateResponse.status,
                  statusText: updateResponse.statusText,
                  body: errorText,
                  file: file.name
                });
                throw new Error(`Update failed: ${updateResponse.status} ${updateResponse.statusText}`);
              }

              const updateResult = await updateResponse.json();

              if (updateResult.success) {
                results.successful.push({
                  filename: file.name,
                  action: 'replaced',
                  id: resolution.existingData._id,
                  preservedData: {
                    markers: resolution.existingData.hasMarkers,
                    cameraSettings: resolution.existingData.hasCameraSettings
                  }
                });

                // Update drag drop status
                if (dragDropFiles.length > 0) {
                  setDragDropFiles(prev => prev.map(f =>
                    f.file === file ? { ...f, status: 'completed' } : f
                  ));
                }
              } else {
                throw new Error(updateResult.message || 'Failed to update existing record');
              }
            } else {
              throw new Error(uploadResult.message || 'File upload failed');
            }
          } catch (error) {
            console.error(`Error replacing file ${file.name}:`, error);
            results.failed.push({
              filename: file.name,
              error: error.message
            });

            // Update drag drop status
            if (dragDropFiles.length > 0) {
              setDragDropFiles(prev => prev.map(f =>
                f.file === file ? { ...f, status: 'error' } : f
              ));
            }
          }
        }
      }

      // Process new files (non-duplicates)
      const newFiles = allFiles.filter(file => {
        const nameWithoutExt = file.name.replace(/\.[^/.]+$/, '');
        return !duplicateResolutions.some(res => res.nameWithoutExt === nameWithoutExt);
      });

      for (const file of newFiles) {
        try {
          // Update status for drag drop files
          if (dragDropFiles.length > 0) {
            setDragDropFiles(prev => prev.map(f =>
              f.file === file ? { ...f, status: 'uploading' } : f
            ));
          }

          // Upload new file
          const formData = new FormData();
          formData.append('files', file);

          const uploadResponse = await fetch('/api/upload/360s', {
            method: 'POST',
            body: formData,
          });

          if (!uploadResponse.ok) {
            const errorText = await uploadResponse.text();
            console.error('New file upload response error:', {
              status: uploadResponse.status,
              statusText: uploadResponse.statusText,
              body: errorText,
              file: file.name
            });
            throw new Error(`Upload failed: ${uploadResponse.status} ${uploadResponse.statusText}`);
          }

          const uploadResult = await uploadResponse.json();

          if (uploadResult.success && uploadResult.data.length > 0) {
            // Create new 360° record
            const threeSixtyData = {
              name: file.name.replace(/\.[^/.]+$/, ''), // Remove extension
              url: uploadResult.data[0].url,
              originalFileName: file.name,
              priority: 0,
            };

            console.log('Sending 360° data to API:', JSON.stringify(threeSixtyData, null, 2));

            // First, test connectivity with the test endpoint
            try {
              console.log('Testing API connectivity...');
              const testResponse = await fetch('/api/360s/test-create', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ test: true }),
              });

              if (testResponse.ok) {
                console.log('API connectivity test passed');
              } else {
                console.warn('API connectivity test failed, but proceeding anyway');
              }
            } catch (testError) {
              console.warn('API connectivity test error:', testError);
            }

            // Retry logic for 360° creation
            let saveResponse;
            let lastError;
            const maxRetries = 3;

            for (let attempt = 1; attempt <= maxRetries; attempt++) {
              try {
                console.log(`Attempt ${attempt}/${maxRetries} to create 360° record...`);

                // Add timeout for each attempt
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

                saveResponse = await fetch('/api/360s', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify(threeSixtyData),
                  signal: controller.signal,
                });

                clearTimeout(timeoutId);

                if (saveResponse.ok) {
                  console.log(`360° record created successfully on attempt ${attempt}`);
                  break; // Success, exit retry loop
                }

                // If it's a 502 error and we have more attempts, try again
                if (saveResponse.status === 502 && attempt < maxRetries) {
                  console.log(`502 error on attempt ${attempt}, retrying in ${attempt * 2} seconds...`);
                  await new Promise(resolve => setTimeout(resolve, attempt * 2000)); // Progressive delay
                  continue;
                }

                // For other errors or final attempt, break and handle below
                break;

              } catch (error) {
                lastError = error;
                console.error(`Attempt ${attempt} failed:`, error);

                if (error.name === 'AbortError') {
                  console.log('Request timed out, retrying...');
                } else if (attempt === maxRetries) {
                  throw error; // Final attempt failed
                }

                // Wait before retry
                if (attempt < maxRetries) {
                  await new Promise(resolve => setTimeout(resolve, attempt * 2000));
                }
              }
            }

            if (!saveResponse.ok) {
              const errorText = await saveResponse.text();
              console.error('Save response error after all retries:', {
                status: saveResponse.status,
                statusText: saveResponse.statusText,
                body: errorText,
                file: file.name,
                sentData: threeSixtyData,
                attempts: maxRetries
              });

              // Try to parse the error response
              let errorDetails = errorText;
              try {
                const errorJson = JSON.parse(errorText);
                errorDetails = errorJson.message || errorJson.error || errorText;
                console.error('Parsed error details:', errorJson);
              } catch (parseError) {
                console.error('Could not parse error response as JSON');
              }

              // Special handling for 502 errors
              if (saveResponse.status === 502) {
                throw new Error(`Server temporarily unavailable. Please try again in a few moments. (Attempted ${maxRetries} times)`);
              }

              throw new Error(`Save failed: ${saveResponse.status} ${saveResponse.statusText} - ${errorDetails}`);
            }

            const saveResult = await saveResponse.json();

            if (saveResult.success) {
              results.successful.push({
                filename: file.name,
                action: 'created',
                id: saveResult.data._id
              });

              // Update status for drag drop files
              if (dragDropFiles.length > 0) {
                setDragDropFiles(prev => prev.map(f =>
                  f.file === file ? { ...f, status: 'completed' } : f
                ));
              }
            } else {
              results.failed.push({
                filename: file.name,
                error: `Failed to save - ${saveResult.message}`
              });

              // Update status for drag drop files
              if (dragDropFiles.length > 0) {
                setDragDropFiles(prev => prev.map(f =>
                  f.file === file ? { ...f, status: 'error' } : f
                ));
              }
            }
          } else {
            results.failed.push({
              filename: file.name,
              error: 'Upload failed'
            });

            // Update status for drag drop files
            if (dragDropFiles.length > 0) {
              setDragDropFiles(prev => prev.map(f =>
                f.file === file ? { ...f, status: 'error' } : f
              ));
            }
          }
        } catch (error) {
          console.error(`Error uploading ${file.name}:`, error);
          results.failed.push({
            filename: file.name,
            error: error.message
          });

          // Update status for drag drop files
          if (dragDropFiles.length > 0) {
            setDragDropFiles(prev => prev.map(f =>
              f.file === file ? { ...f, status: 'error' } : f
            ));
          }
        }
      }

      // Store results and show summary
      setUploadResults(results);

      // Create summary message
      const messages = [];
      if (results.successful.length > 0) {
        const created = results.successful.filter(r => r.action === 'created').length;
        const replaced = results.successful.filter(r => r.action === 'replaced').length;

        if (created > 0) messages.push(`${created} new file(s) uploaded`);
        if (replaced > 0) messages.push(`${replaced} file(s) replaced`);
      }
      if (results.skipped.length > 0) {
        messages.push(`${results.skipped.length} file(s) skipped`);
      }
      if (results.failed.length > 0) {
        messages.push(`${results.failed.length} file(s) failed`);
      }

      if (messages.length > 0) {
        const isSuccess = results.failed.length === 0;
        setErrors({
          submit: isSuccess ? '' : `Some uploads failed: ${results.failed.map(f => f.filename).join(', ')}`
        });

        // Show success message if any files were processed successfully
        if (results.successful.length > 0) {
          console.log('Upload completed:', messages.join(', '));
        }

        // Trigger refresh of the list
        if (onSave && (results.successful.length > 0 || results.skipped.length > 0)) {
          // Clear drag drop files on completion
          setDragDropFiles([]);
          setTimeout(() => {
            window.location.reload(); // Simple refresh for now
          }, 1000);
        }
      }

    } catch (error) {
      console.error('Upload processing error:', error);
      setErrors({ submit: 'Failed to process uploads. Please try again.' });
    } finally {
      setUploading(false);
    }
  };

  // Handle duplicate resolutions from BatchDuplicateHandler
  const handleDuplicateResolutions = async (resolutions) => {
    setDuplicateCheck(prev => ({ ...prev, showHandler: false }));

    // Get all files that were being processed
    const allFiles = dragDropFiles.map(f => f.file);

    // Process uploads with resolutions
    await processFileUploads(allFiles, resolutions);
  };

  // Handle duplicate handler cancellation
  const handleDuplicateCancel = () => {
    setDuplicateCheck({
      isChecking: false,
      duplicates: [],
      newFiles: [],
      showHandler: false,
      singleFileMode: false,
      pendingFile: null
    });
    setUploading(false);
  };

  // Handle single file duplicate confirmation
  const handleSingleDuplicateConfirm = async (duplicateInfo) => {
    try {
      setUploading(true);
      setErrors({ submit: '' }); // Clear previous errors

      const file = duplicateCheck.pendingFile;

      if (!file) {
        throw new Error('No file selected for upload');
      }

      if (!duplicateInfo?.existingData?._id) {
        throw new Error('Invalid duplicate information - missing existing record ID');
      }

      console.log('Starting single file replacement:', {
        filename: file.name,
        existingId: duplicateInfo.existingData._id,
        fileSize: file.size
      });

      // Upload the file
      const formData = new FormData();
      formData.append('files', file);

      console.log('Uploading file to /api/upload/360s...');

      // Create abort controller for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout

      const uploadResponse = await fetch('/api/upload/360s', {
        method: 'POST',
        body: formData,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!uploadResponse.ok) {
        const errorText = await uploadResponse.text();
        console.error('Upload response error:', {
          status: uploadResponse.status,
          statusText: uploadResponse.statusText,
          body: errorText
        });
        throw new Error(`Upload failed: ${uploadResponse.status} ${uploadResponse.statusText}`);
      }

      const uploadResult = await uploadResponse.json();
      console.log('Upload result:', uploadResult);

      if (uploadResult.success && uploadResult.data.length > 0) {
        // Update existing record
        const updateData = {
          url: uploadResult.data[0].url,
          originalFileName: file.name,
        };

        console.log('Updating existing record:', {
          id: duplicateInfo.existingData._id,
          updateData
        });

        // Create abort controller for timeout
        const updateController = new AbortController();
        const updateTimeoutId = setTimeout(() => updateController.abort(), 30000); // 30 second timeout

        const updateResponse = await fetch(`/api/360s/${duplicateInfo.existingData._id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updateData),
          signal: updateController.signal,
        });

        clearTimeout(updateTimeoutId);

        if (!updateResponse.ok) {
          const errorText = await updateResponse.text();
          console.error('Update response error:', {
            status: updateResponse.status,
            statusText: updateResponse.statusText,
            body: errorText
          });

          // If it's a 502 error, try the test endpoint first
          if (updateResponse.status === 502) {
            console.log('502 error detected, trying test endpoint...');
            try {
              const testResponse = await fetch(`/api/360s/${duplicateInfo.existingData._id}/test`, {
                method: 'PATCH',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({ test: true }),
              });

              if (testResponse.ok) {
                console.log('Test endpoint works, the issue is with the main update logic');
              } else {
                console.log('Test endpoint also fails, server issue');
              }
            } catch (testError) {
              console.log('Test endpoint error:', testError);
            }
          }

          // If update fails with 502, try alternative approach
          if (updateResponse.status === 502) {
            console.log('Trying alternative approach: create new record...');

            // Try creating a new record with the same name (this will replace the existing one)
            const alternativeData = {
              name: duplicateInfo.existingData.name,
              url: uploadResult.data[0].url,
              originalFileName: file.name,
              priority: duplicateInfo.existingData.priority || 0,
              // Preserve existing marker data if available
              markerList: duplicateInfo.existingData.markerList || [],
              cameraPosition: duplicateInfo.existingData.cameraPosition || -0.0001,
              _360Rotation: duplicateInfo.existingData._360Rotation || -0.0001,
            };

            const createResponse = await fetch('/api/360s', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(alternativeData),
            });

            if (createResponse.ok) {
              const createResult = await createResponse.json();
              console.log('Alternative create successful:', createResult);

              // Delete the old record
              try {
                await fetch(`/api/360s/${duplicateInfo.existingData._id}`, {
                  method: 'DELETE',
                });
                console.log('Old record deleted successfully');
              } catch (deleteError) {
                console.warn('Failed to delete old record:', deleteError);
              }

              // Treat as successful
              setShowSingleDuplicateModal(false);
              setDuplicateCheck({
                isChecking: false,
                duplicates: [],
                newFiles: [],
                showHandler: false,
                singleFileMode: false,
                pendingFile: null
              });

              console.log('File replacement successful via alternative method, triggering refresh...');

              if (onSave) {
                setTimeout(() => {
                  window.location.reload();
                }, 1000);
              }
              return; // Exit successfully
            }
          }

          throw new Error(`Update failed: ${updateResponse.status} ${updateResponse.statusText}`);
        }

        const updateResult = await updateResponse.json();
        console.log('Update result:', updateResult);

        if (updateResult.success) {
          setShowSingleDuplicateModal(false);
          setDuplicateCheck({
            isChecking: false,
            duplicates: [],
            newFiles: [],
            showHandler: false,
            singleFileMode: false,
            pendingFile: null
          });

          console.log('File replacement successful, triggering refresh...');

          // Trigger refresh
          if (onSave) {
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          }
        } else {
          throw new Error(updateResult.message || 'Failed to update existing record');
        }
      } else {
        throw new Error(uploadResult.message || 'File upload failed');
      }
    } catch (error) {
      console.error('Error replacing file:', error);

      let errorMessage = error.message;
      if (error.name === 'AbortError') {
        errorMessage = 'Upload timed out. Please try again with a smaller file or check your internet connection.';
      } else if (error.message.includes('502')) {
        errorMessage = 'Server error occurred. Please try again in a few moments.';
      } else if (error.message.includes('Failed to fetch')) {
        errorMessage = 'Network error. Please check your internet connection and try again.';
      }

      setErrors({ submit: `Failed to replace file: ${errorMessage}` });
    } finally {
      setUploading(false);
    }
  };

  // Handle single file duplicate skip
  const handleSingleDuplicateSkip = () => {
    setShowSingleDuplicateModal(false);
    setDuplicateCheck({
      isChecking: false,
      duplicates: [],
      newFiles: [],
      showHandler: false,
      singleFileMode: false,
      pendingFile: null
    });
  };

  // Handle single duplicate modal close
  const handleSingleDuplicateClose = () => {
    setShowSingleDuplicateModal(false);
    setDuplicateCheck({
      isChecking: false,
      duplicates: [],
      newFiles: [],
      showHandler: false,
      singleFileMode: false,
      pendingFile: null
    });
  };

  // Handle file replacement confirmation
  const handleFileReplacementConfirm = async (fileInfo) => {
    try {
      setUploading(true);
      setErrors({ submit: '' }); // Clear previous errors

      const file = fileInfo.pendingFile;
      if (!file) {
        throw new Error('No file to upload');
      }

      // Check if this is a Firebase file replacement
      const isFirebaseReplacement = fileInfo.isFileReplacement &&
                                   fileInfo.existingData?.existingUrl &&
                                   fileInfo.existingData.isFirebaseUrl;

      // Step 1: Delete existing Firebase file if applicable
      if (isFirebaseReplacement) {
        console.log('Deleting existing Firebase file before upload:', fileInfo.existingData.existingUrl);
        try {
          const deleteResponse = await fetch('/api/firebase/delete-file', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ url: fileInfo.existingData.existingUrl }),
          });

          const deleteResult = await deleteResponse.json();

          if (deleteResult.success) {
            if (deleteResult.skipped) {
              console.log('Mock Firebase URL deletion skipped (development mode)');
            } else {
              console.log('Successfully deleted existing Firebase file');
            }
          } else {
            console.warn('Failed to delete existing Firebase file, proceeding with upload:', deleteResult.error);
            // Continue with upload even if deletion fails - this is expected for mock URLs
          }
        } catch (deleteError) {
          console.warn('Error deleting existing Firebase file, proceeding with upload:', deleteError);
          // Continue with upload even if deletion fails
        }
      }

      // Step 2: Upload the new file
      console.log('Uploading new file to replace existing one');
      const formData = new FormData();
      formData.append('files', file);

      const uploadResponse = await fetch('/api/upload/360s', {
        method: 'POST',
        body: formData,
      });

      if (!uploadResponse.ok) {
        throw new Error(`Upload failed: ${uploadResponse.status} ${uploadResponse.statusText}`);
      }

      const uploadResult = await uploadResponse.json();
      console.log('Upload result:', uploadResult);

      if (uploadResult.success) {
        // Step 3: Update database record with new URL
        // Handle both single file response format (direct properties) and multiple file format (data array)
        const newFirebaseUrl = uploadResult.data ? uploadResult.data[0].url : uploadResult.url;

        if (!newFirebaseUrl) {
          throw new Error('No Firebase URL received from upload response');
        }
        console.log('Updating database with new Firebase URL:', newFirebaseUrl.substring(0, 100) + '...');

        const updateData = {
          url: newFirebaseUrl,
          originalFileName: file.name,
        };

        const updateResponse = await fetch(`/api/360s/${fileInfo.existingData._id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updateData),
        });

        if (!updateResponse.ok) {
          throw new Error(`Database update failed: ${updateResponse.status} ${updateResponse.statusText}`);
        }

        const updateResult = await updateResponse.json();

        if (updateResult.success) {
          console.log('Firebase file replacement completed successfully');
          setShowFileReplacementModal(false);
          setFileReplacementInfo(null);

          // Clear form and trigger refresh
          setImageFile(null);
          setImagePreview('');

          // Show success message
          if (isFirebaseReplacement) {
            console.log('✅ Firebase file replacement workflow completed successfully');
          }

          if (onSave) {
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          }
        } else {
          throw new Error(updateResult.message || 'Failed to update 360° image in database');
        }
      } else {
        console.error('Upload failed:', uploadResult);
        throw new Error(uploadResult.message || uploadResult.error || 'Upload failed - no data returned from upload service');
      }
    } catch (error) {
      console.error('File replacement error:', error);
      setErrors({ submit: `Failed to replace file: ${error.message}` });
    } finally {
      setUploading(false);
    }
  };

  // Handle file replacement cancellation
  const handleFileReplacementCancel = () => {
    setShowFileReplacementModal(false);
    setFileReplacementInfo(null);
  };

  // Handle file replacement modal close
  const handleFileReplacementClose = () => {
    setShowFileReplacementModal(false);
    setFileReplacementInfo(null);
  };

  const uploadImage = async () => {
    if (!imageFile) return null;

    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('files', imageFile);

      console.log('Uploading single image:', imageFile.name);

      // Create abort controller for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout

      const response = await fetch('/api/upload/360s', {
        method: 'POST',
        body: formData,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Single image upload response error:', {
          status: response.status,
          statusText: response.statusText,
          body: errorText,
          file: imageFile.name
        });
        throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('Single image upload result:', result);

      if (result.success && result.data.length > 0) {
        return result.data[0].url;
      } else {
        throw new Error(result.message || 'Upload failed');
      }
    } catch (error) {
      console.error('Image upload error:', error);

      let errorMessage = error.message;
      if (error.name === 'AbortError') {
        errorMessage = 'Upload timed out. Please try again with a smaller file or check your internet connection.';
      } else if (error.message.includes('502')) {
        errorMessage = 'Server error occurred. Please try again in a few moments.';
      } else if (error.message.includes('Failed to fetch')) {
        errorMessage = 'Network error. Please check your internet connection and try again.';
      }

      throw new Error(errorMessage);
    } finally {
      setUploading(false);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // For single upload mode (editing existing or single file upload)
    if (uploadMode === 'single') {
      if (!formData.name.trim()) {
        newErrors.name = 'Name is required';
      }

      if (!formData.url && !imageFile) {
        newErrors.url = 'Image is required';
      }

      if (formData.priority < 0) {
        newErrors.priority = 'Priority must be 0 or greater';
      }
    }
    // For multiple upload mode, we don't need individual form validation
    // as files are processed automatically

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Handle multiple upload mode
    if (uploadMode === 'multiple' && dragDropFiles.length > 0) {
      await handleMultipleUpload();
      return;
    }

    // Handle single upload mode
    if (!validateForm()) {
      return;
    }

    try {
      // Check for Firebase file replacement when editing existing record
      if (imageFile && threeSixty && threeSixty.url) {
        const isFirebaseURL = threeSixty.url.includes('firebasestorage.googleapis.com');

        if (isFirebaseURL) {
          console.log('Existing 360° record has Firebase URL, showing replacement confirmation');
          setFileReplacementInfo({
            filename: imageFile.name,
            existingData: {
              ...threeSixty,
              hasExistingFile: true,
              existingUrl: threeSixty.url,
              isFirebaseUrl: true
            },
            pendingFile: imageFile,
            isFileReplacement: true // Flag to indicate this is a file replacement, not duplicate
          });
          setShowFileReplacementModal(true);
          return;
        }
      }

      // Check for duplicates in single upload mode when uploading new file
      if (imageFile) {
        const nameWithoutExt = imageFile.name.replace(/\.[^/.]+$/, '');

        // Check if duplicate exists
        const duplicateResponse = await fetch('/api/360s/check-duplicates', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ filenames: [imageFile.name] }),
        });

        const duplicateResult = await duplicateResponse.json();

        if (duplicateResult.success && duplicateResult.data.duplicates.length > 0) {
          const duplicate = duplicateResult.data.duplicates[0];

          // If editing existing record, check if duplicate is the same record
          if (threeSixty && duplicate.existingData._id === threeSixty._id) {
            // Same record - this is just updating the same 360°, no confirmation needed
            console.log('Updating same 360° record, no duplicate confirmation needed');
          } else {
            // Different record or new record - show confirmation modal
            console.log('Duplicate found with different record, showing confirmation modal');
            setFileReplacementInfo({
              filename: imageFile.name,
              existingData: duplicate.existingData || duplicate,
              pendingFile: imageFile
            });
            setShowFileReplacementModal(true);
            return;
          }
        }
      }

      let imageUrl = formData.url;

      // Upload new image if selected
      if (imageFile) {
        imageUrl = await uploadImage();
      }

      const submitData = {
        ...formData,
        url: imageUrl,
      };

      await onSave(submitData);
    } catch (error) {
      console.error('Form submission error:', error);
      setErrors({ submit: 'Failed to save 360 image. Please try again.' });
    }
  };

  return (
    <>
      {/* Batch Duplicate Handler */}
      <BatchDuplicateHandler
        duplicates={duplicateCheck.duplicates}
        onAllResolved={handleDuplicateResolutions}
        onCancel={handleDuplicateCancel}
        isOpen={duplicateCheck.showHandler}
      />

      {/* Single File Duplicate Confirmation Modal */}
      <DuplicateConfirmationModal
        isOpen={showSingleDuplicateModal}
        onClose={handleSingleDuplicateClose}
        duplicateInfo={duplicateCheck.duplicates.length > 0 ? {
          filename: duplicateCheck.duplicates[0].filename || duplicateCheck.pendingFile?.name || '',
          existingData: duplicateCheck.duplicates[0].existingData || duplicateCheck.duplicates[0]
        } : null}
        onConfirm={handleSingleDuplicateConfirm}
        onSkip={handleSingleDuplicateSkip}
      />

      {/* File Replacement Confirmation Modal */}
      <FileReplacementModal
        isOpen={showFileReplacementModal}
        onClose={handleFileReplacementClose}
        fileInfo={fileReplacementInfo}
        onConfirm={handleFileReplacementConfirm}
        onCancel={handleFileReplacementCancel}
      />

      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-900">
            {threeSixty ? 'Edit 360° Image' : 'Create New 360° Image'}
          </h2>

        {/* Upload Mode Toggle (only for new images) */}
        {!threeSixty && (
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-600">Upload Mode:</span>
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                type="button"
                onClick={() => setUploadMode('single')}
                className={`px-3 py-1 text-sm rounded-md transition-colors ${
                  uploadMode === 'single'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Single File
              </button>
              <button
                type="button"
                onClick={() => setUploadMode('multiple')}
                className={`px-3 py-1 text-sm rounded-md transition-colors ${
                  uploadMode === 'multiple'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Multiple Files
              </button>
            </div>
          </div>
        )}
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Original File Name (read-only if editing) */}
        {formData.originalFileName && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Original File Name
            </label>
            <input
              type="text"
              value={formData.originalFileName}
              readOnly
              className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
            />
          </div>
        )}

        {/* Name Field (for single upload mode) */}
        {uploadMode === 'single' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Name *
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.name ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Enter a name for this 360° image"
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name}</p>
            )}
          </div>
        )}

        {/* Priority Field (for single upload mode) */}
        {uploadMode === 'single' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Priority
            </label>
            <input
              type="number"
              name="priority"
              value={formData.priority}
              onChange={handleInputChange}
              min="0"
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.priority ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="0"
            />
            {errors.priority && (
              <p className="mt-1 text-sm text-red-600">{errors.priority}</p>
            )}
            <p className="mt-1 text-sm text-gray-500">
              Higher priority images appear first in the list (0 = lowest priority)
            </p>
          </div>
        )}

        {/* Image Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            360° Image *
          </label>

          {uploadMode === 'single' ? (
            <>
              {/* Single File Upload - Traditional Method */}
              {/* Image Preview */}
              {imagePreview && (
                <div className="mb-4">
                  <img
                    src={imagePreview}
                    alt="Preview"
                    className="w-64 h-32 object-cover rounded-md border border-gray-300"
                  />
                </div>
              )}

              {/* File Input */}
              <div className="space-y-3">
                <div className="flex items-center space-x-4">
                  <label className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer">
                    <MdCloudUpload className="mr-2" />
                    Choose 360° Image
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleImageChange}
                      className="hidden"
                    />
                  </label>

                  {imageFile && (
                    <span className="text-sm text-gray-600">
                      {imageFile.name}
                    </span>
                  )}
                </div>

                <p className="text-sm text-gray-500">
                  Upload a single 360° panoramic image. Recommended format: equirectangular projection. Max 20MB.
                </p>
              </div>

              {errors.url && (
                <p className="mt-1 text-sm text-red-600">{errors.url}</p>
              )}
            </>
          ) : (
            <>
              {/* Multiple File Upload - Drag and Drop */}
              <DragDropUpload
                onFilesSelected={handleDragDropFiles}
                maxFiles={10}
                maxSize={20 * 1024 * 1024} // 20MB
                acceptedTypes={['image/jpeg', 'image/png', 'image/tiff']}
                disabled={uploading}
              />

              {dragDropFiles.length > 0 && (
                <div className="mt-4 bg-blue-50 border border-blue-200 rounded-md p-3">
                  <div className="flex items-start space-x-2">
                    <MdInfo className="text-blue-600 mt-0.5 flex-shrink-0" />
                    <div className="text-sm text-blue-800">
                      <p className="font-medium">Ready to Upload:</p>
                      <ul className="mt-1 space-y-1 text-xs">
                        <li>• {dragDropFiles.length} file(s) selected</li>
                        <li>• Each file will be created as a separate 360° image</li>
                        <li>• Files will be automatically named based on filename</li>
                        <li>• All files will have priority 0 (can be edited later)</li>
                        <li>• Duplicate filenames will be detected and require confirmation</li>
                      </ul>
                    </div>
                  </div>
                </div>
              )}

              {/* Duplicate Check Status */}
              {duplicateCheck.isChecking && (
                <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded-md p-3">
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600"></div>
                    <span className="text-sm text-yellow-800">Checking for duplicate filenames...</span>
                  </div>
                </div>
              )}

              {/* Upload Results Summary */}
              {uploadResults.successful && uploadResults.successful.length > 0 ||
               uploadResults.failed && uploadResults.failed.length > 0 ||
               uploadResults.skipped && uploadResults.skipped.length > 0 ? (
                <div className="mt-4 bg-gray-50 border border-gray-200 rounded-md p-3">
                  <div className="text-sm text-gray-800">
                    <p className="font-medium mb-2">Upload Results:</p>
                    <div className="space-y-1 text-xs">
                      {uploadResults.successful && uploadResults.successful.length > 0 && (
                        <div className="text-green-600">
                          ✓ {uploadResults.successful.filter(r => r.action === 'created').length} new files uploaded
                          {uploadResults.successful.filter(r => r.action === 'replaced').length > 0 && (
                            <span>, {uploadResults.successful.filter(r => r.action === 'replaced').length} files replaced</span>
                          )}
                        </div>
                      )}
                      {uploadResults.skipped && uploadResults.skipped.length > 0 && (
                        <div className="text-yellow-600">
                          ⚠ {uploadResults.skipped.length} files skipped
                        </div>
                      )}
                      {uploadResults.failed && uploadResults.failed.length > 0 && (
                        <div className="text-red-600">
                          ✗ {uploadResults.failed.length} files failed
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ) : null}
            </>
          )}
        </div>

        {/* Submit Error */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-red-600 text-sm">{errors.submit}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 flex items-center"
          >
            <MdCancel className="mr-2" />
            Cancel
          </button>
          
          <button
            type="submit"
            disabled={isLoading || uploading || duplicateCheck.isChecking || duplicateCheck.showHandler || (uploadMode === 'multiple' && dragDropFiles.length === 0)}
            className={`px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center ${
              isLoading || uploading || duplicateCheck.isChecking || duplicateCheck.showHandler || (uploadMode === 'multiple' && dragDropFiles.length === 0)
                ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <MdSave className="mr-2" />
            {duplicateCheck.isChecking
              ? 'Checking Duplicates...'
              : duplicateCheck.showHandler
                ? 'Resolving Duplicates...'
                : isLoading || uploading
                  ? 'Processing...'
                  : uploadMode === 'multiple'
                    ? `Upload ${dragDropFiles.length} File(s)`
                    : threeSixty
                      ? 'Update 360° Image'
                      : 'Save 360° Image'
            }
          </button>
        </div>
      </form>
      </div>
    </>
  );
}
