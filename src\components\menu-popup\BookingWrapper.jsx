'use client'
import { useEffect, useState } from 'react'
import { useContextExperience } from '@/contexts/useContextExperience'
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'
import BookingFormComponent from '../BookingFormComponent'
import Image from 'next/image'

function PagePopup({item}) {
  const {image,body,title,secondaryEntries}=item
  console.log('BookingWrapper pagepopup:',secondaryEntries)

  return(
    <div className='flex flex-col mt-20 w-full h-fit items-center justify-start gap-5'>
      <div className='flex flex-col w-full h-fit items-center justify-start gap-10'>
        <div className='w-full h-60 relative'>
          <Image src={image} alt='page image' className='object-cover' fill/>
        </div>
        <div className='flex w-full h-fit'>
          <h1 className='w-1/4 text-6xl text-left leading-12 uppercase'>
            {title}
          </h1>
          <p className='w-3/4 text-left leading-7'>
            {body}
          </p>
        </div>
        {secondaryEntries?.map((i,index)=>(
          <div key={index} className='flex flex-col w-full h-fit items-center justify-start gap-10'>
            <div className='w-full h-60 relative'>
              <Image src={i?.image} alt='page image' className='object-cover' fill/>
            </div>
            <div className='flex w-full h-fit'>
              <h1 className='w-1/4 text-6xl text-left leading-12 uppercase'>
                {i?.title}
              </h1>
              <p className='w-3/4 text-left leading-7'>
                {i?.body}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default function BookingWrapper() {
    const lineClass2='w-full border-1 border-gray-400/30'
    const lineClass='w-full border-1 mt-2 border-gray-400/30'
    const {experienceState,disptachExperience}=useContextExperience()
    const [pageInfo,setPageInfo]=useState([])
    const [closePopup,setClosePopup]=useState(false)
    const [showPages,setShowPages]=useState(false)
    // console.log('PopupWrapper:',query)

    const handleBookingClose=()=>{
      // console.log('booking close')
      disptachExperience({type:ACTIONS_EXPERIENCE_STATE.POPUP_BOOKING_TOGGLE})
      // setClosePopup(true)
    }

    const handlePopupClose=()=>{
      {experienceState?.showTheIslandPage && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.SHOW_ISLAND_PAGE})}
      {experienceState?.showExperiencePage && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.SHOW_EXPERIENCE_PAGE})}
      {experienceState?.showTestimonials && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.SHOW_TESTIMONIALS_PAGE})}
      {experienceState?.showLocationAndContacts && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.SHOW_LOCATION_AND_CONTACTS_PAGE})}
    }

    const closeButton=()=>{
      console.log('close button click')
      setClosePopup(false)
      setShowPages(false)
    }    

    useEffect(() => {
      const fetchPageInfo = async () => {
        try {
          const res = await fetch('/api/pages', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json'
            }
          });
          
          if (!res.ok) {
            throw new Error('Failed to fetch page info');
          }
          
          const data = await res.json();
          // console.log(data)
          setPageInfo(data?.data)
          return data;
        } catch (error) {
          console.error('Error fetching page info:', error);
        }
      };
      fetchPageInfo()
    }, [])
    
    // console.log('BookingWrapper:',pageInfo?.section==='the island')

  return (
    // (closePopup &&
      <>
        {experienceState?.showBookingPopup && <div className='popup-wrapper flex z-10 absolute top-0 left-0 w-full h-full bg-black/75'>
          <div 
            onClick={handleBookingClose} 
            className=" flex z-50 items-center justify-center absolute top-0 right-[104px] h-[75px] w-[96px] cursor-pointer"
            >
            <div className={`rotate-45  bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
            <div className={`-rotate-45 bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
          </div>
          <div className='flex relative top-0 left0 h-full px-5 md:max-w-4xl mx-auto overflow-x-hidden'>
            <BookingFormComponent/>
          </div>
        </div>}
        {((experienceState?.showTheIslandPage||experienceState?.showExperiencePage||experienceState?.showTestimonials||experienceState?.showLocationAndContacts) && pageInfo?.length>0) &&
          <div className='popup-wrapper text-white flex z-10 absolute top-0 left-0 w-full h-full bg-black/85 overflow-hidden'>
            <div 
              onClick={handlePopupClose} 
              className=" flex z-40 items-center justify-center absolute top-0 right-[104px] h-[75px] w-[96px] cursor-pointer"
              >
              <div className={`rotate-45  bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
              <div className={`-rotate-45 bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
            </div>
            <div className='flex relative top-0 left0 h-fit px-5 md:max-w-4xl mx-auto overflow-x-hidden'>
              {pageInfo?.map((i,index)=>
                // {console.log(i?.section=='the island',i)}
                i?.section==='the island' && experienceState?.showTheIslandPage && <PagePopup key={index} item={i}/>
              )}
            </div>
          </div>}
      </>
    // )
  )
}
